[{"C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\dashboard-card.tsx": "1", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\auth.ts": "2", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\database.ts": "3", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\utils.ts": "4", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\layout.tsx": "6", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\components\\ui\\dashboard-card.tsx": "8", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\lib\\auth.ts": "9", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\lib\\database.ts": "10", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\lib\\utils.ts": "11", "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\types\\next-auth.d.ts": "12"}, {"size": 1037, "mtime": 1750433999424, "results": "13", "hashOfConfig": "14"}, {"size": 1718, "mtime": 1750437968855, "results": "15", "hashOfConfig": "14"}, {"size": 271, "mtime": 1750433874619, "results": "16", "hashOfConfig": "14"}, {"size": 166, "mtime": 1750433890252, "results": "17", "hashOfConfig": "14"}, {"size": 2713, "mtime": 1750435194033, "results": "18", "hashOfConfig": "14"}, {"size": 689, "mtime": 1750361517867, "results": "19", "hashOfConfig": "14"}, {"size": 104, "mtime": 1750434115787, "results": "20", "hashOfConfig": "14"}, {"size": 1037, "mtime": 1750435154954, "results": "21", "hashOfConfig": "14"}, {"size": 467, "mtime": 1750437345626, "results": "22", "hashOfConfig": "14"}, {"size": 271, "mtime": 1750435161898, "results": "23", "hashOfConfig": "14"}, {"size": 166, "mtime": 1750435179070, "results": "24", "hashOfConfig": "14"}, {"size": 374, "mtime": 1750435818442, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "19o6u7n", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\components\\ui\\dashboard-card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\auth.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\database.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\components\\ui\\dashboard-card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\lib\\auth.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\lib\\database.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\codes\\crm-staff\\inno-crm-students\\src\\types\\next-auth.d.ts", [], []]