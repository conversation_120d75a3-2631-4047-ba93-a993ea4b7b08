import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('Seeding staff users...')

  // Create admin user
  const adminPassword = await bcrypt.hash('admin123', 12)
  const admin = await prisma.user.upsert({
    where: { phone: '+998901234567' },
    update: {},
    create: {
      phone: '+998901234567',
      email: '<EMAIL>',
      name: 'System Administrator',
      role: 'ADMIN',
      password: adminPassword,
    },
  })

  // Create manager user
  const managerPassword = await bcrypt.hash('manager123', 12)
  const manager = await prisma.user.upsert({
    where: { phone: '+998901234568' },
    update: {},
    create: {
      phone: '+998901234568',
      email: '<EMAIL>',
      name: 'Academic Manager',
      role: 'MANAGE<PERSON>',
      password: managerPassword,
    },
  })

  // Create teacher user
  const teacherPassword = await bcrypt.hash('teacher123', 12)
  const teacher = await prisma.user.upsert({
    where: { phone: '+998901234569' },
    update: {},
    create: {
      phone: '+998901234569',
      email: '<EMAIL>',
      name: 'John Smith',
      role: 'TEACHER',
      password: teacherPassword,
      teacherProfile: {
        create: {
          subject: 'English',
          experience: 5,
          salary: 1000,
          branch: 'main',
          tier: 'B_LEVEL',
        },
      },
    },
  })

  console.log('Staff users created:')
  console.log('Admin:', { phone: admin.phone, password: 'admin123' })
  console.log('Manager:', { phone: manager.phone, password: 'manager123' })
  console.log('Teacher:', { phone: teacher.phone, password: 'teacher123' })
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
