import NextAuth from "next-auth"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      name: string
      phone: string
      email?: string | null
      role: string
    }
  }

  interface User {
    id: string
    name: string
    phone: string
    email?: string | null
    role: string
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    role?: string
    phone?: string
  }
}
