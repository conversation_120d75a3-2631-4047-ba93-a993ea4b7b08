'use client'

import { useSearchParams } from 'next/navigation'
import Link from 'next/link'

export default function AuthErrorPage() {
  const searchParams = useSearchParams()
  const error = searchParams.get('error')

  const getErrorMessage = (error: string | null) => {
    switch (error) {
      case 'CredentialsSignin':
        return 'Invalid phone number or password. Please try again.'
      case 'AccessDenied':
        return 'Access denied. You do not have permission to access this system.'
      case 'Configuration':
        return 'There is a problem with the server configuration.'
      default:
        return 'An unexpected error occurred. Please try again.'
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-base-200">
      <div className="card w-full max-w-md bg-base-100 shadow-xl">
        <div className="card-body text-center">
          <div className="text-error text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-error mb-4">Authentication Error</h1>
          
          <div className="alert alert-error mb-6">
            <span>{getErrorMessage(error)}</span>
          </div>

          <div className="space-y-4">
            <Link href="/auth/signin" className="btn btn-primary w-full">
              Try Again
            </Link>
            
            <Link href="/" className="btn btn-ghost w-full">
              Go Home
            </Link>
          </div>

          <div className="divider"></div>

          <p className="text-sm text-base-content/70">
            If you continue to experience issues, please contact your administrator.
          </p>
        </div>
      </div>
    </div>
  )
}
