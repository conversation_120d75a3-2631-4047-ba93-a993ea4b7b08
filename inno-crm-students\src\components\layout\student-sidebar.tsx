"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { useSession, signOut } from "next-auth/react"
import { cn } from "@/lib/utils"
import { 
  BookOpen, 
  Calendar, 
  Award, 
  CreditCard, 
  User,
  MessageSquare,
  BarChart3,
  LogOut
} from "lucide-react"

const navigation = [
  { name: "Dashboard", href: "/dashboard", icon: BarChart3 },
  { name: "My Classes", href: "/dashboard/classes", icon: Calendar },
  { name: "Assessments", href: "/dashboard/assessments", icon: Award },
  { name: "Progress", href: "/dashboard/progress", icon: BookOpen },
  { name: "Payments", href: "/dashboard/payments", icon: CreditCard },
  { name: "Messages", href: "/dashboard/messages", icon: MessageSquare },
  { name: "Profile", href: "/dashboard/profile", icon: User },
]

export function StudentSidebar() {
  const pathname = usePathname()
  const { data: session } = useSession()

  const handleSignOut = () => {
    signOut({ callbackUrl: '/auth/signin' })
  }

  return (
    <div className="drawer-side">
      <label htmlFor="drawer-toggle" className="drawer-overlay"></label>
      <aside className="min-h-full w-64 bg-base-200 flex flex-col">
        <div className="p-4">
          <h1 className="text-xl font-bold text-primary">
            Student Portal
          </h1>
        </div>

        <div className="flex-1">
          <ul className="menu p-4 space-y-2">
            {navigation.map((item) => {
              const Icon = item.icon
              const isActive = pathname === item.href
              
              return (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className={cn(
                      "flex items-center space-x-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",
                      isActive
                        ? "bg-primary text-primary-content"
                        : "text-base-content hover:bg-base-300"
                    )}
                  >
                    <Icon className="h-5 w-5" />
                    <span>{item.name}</span>
                  </Link>
                </li>
              )
            })}
          </ul>
        </div>

        {/* User info and sign out */}
        {session && (
          <div className="p-4 border-t border-base-300">
            <div className="flex items-center space-x-3 mb-3">
              <div className="avatar placeholder">
                <div className="bg-neutral text-neutral-content rounded-full w-10">
                  <User className="h-5 w-5" />
                </div>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-base-content truncate">
                  {session.user.name}
                </p>
                <p className="text-xs text-base-content/70 truncate">
                  Student
                </p>
              </div>
            </div>
            <button
              onClick={handleSignOut}
              className="btn btn-ghost btn-sm w-full justify-start"
            >
              <LogOut className="h-4 w-4" />
              Sign Out
            </button>
          </div>
        )}
      </aside>
    </div>
  )
}
