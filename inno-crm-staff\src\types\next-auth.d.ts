import "next-auth"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      name: string
      phone: string
      email?: string
      role: string
      teacherProfile?: {
        id: string
        subject: string
        experience?: number
        salary?: number
        branch: string
        photoUrl?: string
        tier: string
      }
    }
  }

  interface User {
    id: string
    name: string
    phone: string
    email?: string
    role: string
    teacherProfile?: {
      id: string
      subject: string
      experience?: number
      salary?: number
      branch: string
      photoUrl?: string
      tier: string
    }
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    role: string
    phone: string
    teacherProfile?: {
      id: string
      subject: string
      experience?: number
      salary?: number
      branch: string
      photoUrl?: string
      tier: string
    }
  }
}
