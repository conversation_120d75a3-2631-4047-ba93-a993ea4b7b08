import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { redirect } from 'next/navigation'
import { prisma } from '@/lib/database'
import { CreditCard, Calendar, CheckCircle, AlertCircle } from 'lucide-react'

export default async function PaymentsPage() {
  const session = await getServerSession(authOptions)

  if (!session) {
    redirect('/auth/signin')
  }

  // Fetch student payment data
  const student = await prisma.student.findUnique({
    where: { userId: session.user.id },
    include: {
      payments: {
        orderBy: { createdAt: 'desc' }
      }
    }
  })

  if (!student) {
    redirect('/auth/signin')
  }

  // Calculate payment statistics
  const totalPaid = student.payments
    .filter(p => p.status === 'PAID')
    .reduce((sum, payment) => sum + payment.amount.toNumber(), 0)
  
  const totalDebt = student.payments
    .filter(p => p.status === 'DEBT')
    .reduce((sum, payment) => sum + payment.amount.toNumber(), 0)

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PAID': return 'badge-success'
      case 'DEBT': return 'badge-error'
      case 'REFUNDED': return 'badge-warning'
      default: return 'badge-ghost'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PAID': return <CheckCircle className="h-4 w-4" />
      case 'DEBT': return <AlertCircle className="h-4 w-4" />
      default: return <CreditCard className="h-4 w-4" />
    }
  }

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-base-content">
          Payment History
        </h1>
        <p className="text-base-content/70 mt-2">
          View your payment records and account balance
        </p>
      </div>

      {/* Payment Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="card-title text-sm font-medium text-base-content/70">
                  Total Paid
                </h2>
                <p className="text-2xl font-bold text-success">
                  ${totalPaid}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-success" />
            </div>
          </div>
        </div>

        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="card-title text-sm font-medium text-base-content/70">
                  Outstanding Debt
                </h2>
                <p className="text-2xl font-bold text-error">
                  ${totalDebt}
                </p>
              </div>
              <AlertCircle className="h-8 w-8 text-error" />
            </div>
          </div>
        </div>

        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="card-title text-sm font-medium text-base-content/70">
                  Total Payments
                </h2>
                <p className="text-2xl font-bold text-base-content">
                  {student.payments.length}
                </p>
              </div>
              <CreditCard className="h-8 w-8 text-primary" />
            </div>
          </div>
        </div>
      </div>

      {/* Payment History Table */}
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <h2 className="card-title mb-4">Payment History</h2>
          
          {student.payments.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="table table-zebra w-full">
                <thead>
                  <tr>
                    <th>Date</th>
                    <th>Description</th>
                    <th>Amount</th>
                    <th>Method</th>
                    <th>Status</th>
                    <th>Due Date</th>
                  </tr>
                </thead>
                <tbody>
                  {student.payments.map((payment) => (
                    <tr key={payment.id}>
                      <td>
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-base-content/50" />
                          <span>
                            {payment.paidDate 
                              ? new Date(payment.paidDate).toLocaleDateString()
                              : new Date(payment.createdAt).toLocaleDateString()
                            }
                          </span>
                        </div>
                      </td>
                      <td>
                        <div>
                          <div className="font-medium">
                            {payment.description || 'Course Payment'}
                          </div>
                          {payment.transactionId && (
                            <div className="text-sm text-base-content/70">
                              ID: {payment.transactionId}
                            </div>
                          )}
                        </div>
                      </td>
                      <td>
                        <span className="font-bold">
                          ${payment.amount.toNumber()}
                        </span>
                      </td>
                      <td>
                        <span className="badge badge-outline">
                          {payment.method}
                        </span>
                      </td>
                      <td>
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(payment.status)}
                          <span className={`badge ${getStatusColor(payment.status)}`}>
                            {payment.status}
                          </span>
                        </div>
                      </td>
                      <td>
                        {payment.dueDate ? (
                          <span className={
                            new Date(payment.dueDate) < new Date() && payment.status === 'DEBT'
                              ? 'text-error'
                              : 'text-base-content'
                          }>
                            {new Date(payment.dueDate).toLocaleDateString()}
                          </span>
                        ) : (
                          <span className="text-base-content/50">N/A</span>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8">
              <CreditCard className="h-12 w-12 text-base-content/30 mx-auto mb-4" />
              <p className="text-base-content/70">No payment history found</p>
            </div>
          )}
        </div>
      </div>

      {/* Outstanding Debts Alert */}
      {totalDebt > 0 && (
        <div className="mt-6">
          <div className="alert alert-error">
            <AlertCircle className="h-5 w-5" />
            <div>
              <h3 className="font-bold">Outstanding Payment</h3>
              <div className="text-sm">
                You have ${totalDebt} in outstanding payments. Please contact the administration to resolve this.
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
