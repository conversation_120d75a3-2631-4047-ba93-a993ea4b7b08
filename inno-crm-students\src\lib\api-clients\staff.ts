// API client for communicating with the staff service

interface GroupData {
  id: string
  name: string
  courseId: string
  teacherId: string
  capacity: number
  schedule: any
  room?: string
  branch: string
  startDate: string
  endDate: string
  isActive: boolean
}

interface TeacherData {
  id: string
  name: string
  subject: string
  branch: string
  photoUrl?: string
}

interface PaymentData {
  id: string
  studentId: string
  amount: number
  method: string
  status: string
  description?: string
  dueDate?: string
  paidDate?: string
}

class StaffApiClient {
  private baseUrl: string
  private apiKey: string

  constructor() {
    this.baseUrl = process.env.STAFF_API_URL || 'http://localhost:3001'
    this.apiKey = process.env.STAFF_API_KEY || ''
  }

  private async request(endpoint: string, options: RequestInit = {}) {
    const url = `${this.baseUrl}/api${endpoint}`
    
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': this.apiKey,
        'X-Server-Source': 'students',
        ...options.headers,
      },
    })

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: 'Unknown error' }))
      throw new Error(error.error || `HTTP ${response.status}`)
    }

    return response.json()
  }

  // Get group information from staff database
  async getGroup(id: string): Promise<GroupData> {
    return this.request(`/inter-server/groups/${id}`)
  }

  // Get teacher information from staff database
  async getTeacher(id: string): Promise<TeacherData> {
    return this.request(`/inter-server/teachers/${id}`)
  }

  // Sync group data from staff database
  async syncGroup(id: string): Promise<GroupData> {
    return this.request(`/inter-server/groups/${id}/sync`, {
      method: 'POST',
    })
  }

  // Sync teacher data from staff database
  async syncTeacher(id: string): Promise<TeacherData> {
    return this.request(`/inter-server/teachers/${id}/sync`, {
      method: 'POST',
    })
  }

  // Report student attendance to staff database
  async reportAttendance(data: {
    studentId: string
    classReferenceId: string
    groupId: string
    status: string
    notes?: string
  }) {
    return this.request('/inter-server/attendance', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  // Report assessment results to staff database
  async reportAssessment(data: {
    studentId: string
    groupId: string
    testName: string
    type: string
    score?: number
    maxScore?: number
    passed: boolean
    results?: any
  }) {
    return this.request('/inter-server/assessments', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  // Get payment information from staff database
  async getPaymentOverview(studentId: string): Promise<PaymentData[]> {
    return this.request(`/inter-server/payments/student/${studentId}`)
  }

  // Notify staff about student status changes
  async notifyStudentStatusChange(studentId: string, status: string, reason?: string) {
    return this.request('/inter-server/students/status-change', {
      method: 'POST',
      body: JSON.stringify({
        studentId,
        status,
        reason,
        timestamp: new Date().toISOString()
      }),
    })
  }

  // Request group assignment for student
  async requestGroupAssignment(studentId: string, preferredLevel: string) {
    return this.request('/inter-server/groups/assign-request', {
      method: 'POST',
      body: JSON.stringify({
        studentId,
        preferredLevel,
        timestamp: new Date().toISOString()
      }),
    })
  }
}

export const staffApiClient = new StaffApiClient()
