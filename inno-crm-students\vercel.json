{"framework": "nextjs", "buildCommand": "prisma generate && npm run build", "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}], "redirects": [{"source": "/", "destination": "/dashboard", "permanent": false}], "env": {"NEXTAUTH_URL": "https://students.innovativecentre.net"}}