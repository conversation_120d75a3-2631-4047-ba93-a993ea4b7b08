# Student Portal - Innovative Centre

A student-facing portal for Innovative Centre, providing access to personal dashboard, course information, and progress tracking.

## Features

- **Personal Dashboard**: Overview of current courses and progress
- **Authentication**: Secure student login
- **Profile Management**: View and update personal information
- **Payment History**: Track payments and outstanding balances
- **Course Progress**: Monitor academic progress and assessments
- **Class Schedule**: View current group and class information
- **Assessment Results**: Access test scores and feedback

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js
- **Styling**: Tailwind CSS with Ripple UI
- **Deployment**: Vercel

## Getting Started

### Prerequisites

- Node.js 18+
- PostgreSQL database (Neon recommended)
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

4. Configure your database URL and other environment variables in `.env.local`

5. Set up the database:
```bash
npm run db:push
npm run db:seed
```

6. Start the development server:
```bash
npm run dev
```

The application will be available at [http://localhost:3002](http://localhost:3002)

## Environment Variables

```bash
DATABASE_URL="postgresql://username:password@host:port/database"
NEXTAUTH_URL="http://localhost:3002"
NEXTAUTH_SECRET="your-secret-key-here"
STAFF_API_URL="http://localhost:3001"
STAFF_API_KEY="your-staff-api-key"
```

## Default Credentials

After running the seed script:

- **Student 1**: `+998901234570` / `student123`
- **Student 2**: `+998901234572` / `student456`

## Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run db:push` - Push database schema
- `npm run db:studio` - Open Prisma Studio
- `npm run db:seed` - Seed database with test data

## Project Structure

```
src/
├── app/                 # Next.js App Router
│   ├── api/            # API routes
│   ├── auth/           # Authentication pages
│   └── dashboard/      # Main application pages
├── components/         # Reusable components
│   ├── layout/        # Layout components
│   └── ui/            # UI components
├── lib/               # Utility libraries
└── types/             # TypeScript type definitions
```

## Student Features

### Dashboard
- Current level and progress overview
- Attendance statistics
- Recent assessments
- Payment summary

### Profile
- Personal information management
- Emergency contact details
- Current group information

### Payments
- Payment history
- Outstanding balances
- Transaction details

## Deployment

Deploy to Vercel with custom domain:

```bash
vercel --prod
```

Configure domain: `students.innovativecentre.net`

See [DEPLOYMENT.md](../DEPLOYMENT.md) for detailed deployment instructions.

## License

Private - Innovative Centre
