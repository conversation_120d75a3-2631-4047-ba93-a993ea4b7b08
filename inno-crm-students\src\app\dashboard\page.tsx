import { DashboardCard } from "@/components/ui/dashboard-card"
import { BookOpen, Calendar, Award, CreditCard } from "lucide-react"

export default function StudentDashboard() {
  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-base-content">
          Student Portal
        </h1>
        <p className="text-base-content/70 mt-2">
          Welcome to your learning dashboard
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <DashboardCard
          title="Current Level"
          value="A1"
          description="English Level"
          icon={<BookOpen className="h-8 w-8" />}
        />
        <DashboardCard
          title="Attendance"
          value="95%"
          description="This month"
          icon={<Calendar className="h-8 w-8" />}
        />
        <DashboardCard
          title="Assessments"
          value="0"
          description="Completed"
          icon={<Award className="h-8 w-8" />}
        />
        <DashboardCard
          title="Balance"
          value="$0"
          description="Account balance"
          icon={<CreditCard className="h-8 w-8" />}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h2 className="card-title">Upcoming Classes</h2>
            <div className="space-y-4">
              <div className="alert alert-info">
                <span>No upcoming classes scheduled</span>
              </div>
            </div>
          </div>
        </div>

        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h2 className="card-title">Recent Assessments</h2>
            <div className="space-y-4">
              <div className="text-sm text-base-content/70">
                No assessments completed yet.
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-6">
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h2 className="card-title">Quick Actions</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button className="btn btn-primary">
                View Schedule
              </button>
              <button className="btn btn-secondary">
                Take Assessment
              </button>
              <button className="btn btn-accent">
                View Progress
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
