import { DashboardCard } from "@/components/ui/dashboard-card"
import { BookOpen, Calendar, Award, CreditCard } from "lucide-react"
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { redirect } from 'next/navigation'
import { prisma } from '@/lib/database'

export default async function StudentDashboard() {
  const session = await getServerSession(authOptions)

  if (!session) {
    redirect('/auth/signin')
  }

  // Fetch student data
  const student = await prisma.student.findUnique({
    where: { userId: session.user.id },
    include: {
      currentGroupReference: {
        include: {
          teacherReference: true,
        }
      },
      enrollments: {
        include: {
          groupReference: true,
        },
        orderBy: { createdAt: 'desc' },
        take: 5,
      },
      payments: {
        orderBy: { createdAt: 'desc' },
        take: 5,
      },
      attendances: {
        orderBy: { createdAt: 'desc' },
        take: 10,
      },
      assessments: {
        orderBy: { createdAt: 'desc' },
        take: 5,
      }
    }
  })

  if (!student) {
    redirect('/auth/signin')
  }

  // Calculate statistics
  const totalPayments = student.payments.reduce((sum, payment) =>
    sum + payment.amount.toNumber(), 0
  )

  const attendanceRate = student.attendances.length > 0
    ? (student.attendances.filter(a => a.status === 'PRESENT').length / student.attendances.length) * 100
    : 0

  const completedAssessments = student.assessments.filter(a => a.completedAt).length
  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-base-content">
          Welcome back, {student.user.name}!
        </h1>
        <p className="text-base-content/70 mt-2">
          Track your progress and stay updated with your courses
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <DashboardCard
          title="Current Level"
          value={student.level}
          description="English Level"
          icon={<BookOpen className="h-8 w-8" />}
        />
        <DashboardCard
          title="Attendance"
          value={`${Math.round(attendanceRate)}%`}
          description="Overall rate"
          icon={<Calendar className="h-8 w-8" />}
        />
        <DashboardCard
          title="Assessments"
          value={completedAssessments}
          description="Completed"
          icon={<Award className="h-8 w-8" />}
        />
        <DashboardCard
          title="Total Paid"
          value={`$${totalPayments}`}
          description="All time"
          icon={<CreditCard className="h-8 w-8" />}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h2 className="card-title">Current Group</h2>
            <div className="space-y-4">
              {student.currentGroupReference ? (
                <div className="p-4 bg-base-200 rounded-lg">
                  <h3 className="font-semibold">{student.currentGroupReference.name}</h3>
                  <p className="text-sm text-base-content/70">
                    Teacher: {student.currentGroupReference.teacherReference.name}
                  </p>
                  <p className="text-sm text-base-content/70">
                    Course: {student.currentGroupReference.courseName}
                  </p>
                  <p className="text-sm text-base-content/70">
                    Room: {student.currentGroupReference.room || 'TBA'}
                  </p>
                </div>
              ) : (
                <div className="alert alert-info">
                  <span>No group assigned yet</span>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h2 className="card-title">Recent Assessments</h2>
            <div className="space-y-4">
              {student.assessments.length > 0 ? (
                student.assessments.slice(0, 3).map((assessment) => (
                  <div key={assessment.id} className="p-3 bg-base-200 rounded-lg">
                    <div className="flex justify-between items-center">
                      <div>
                        <h4 className="font-medium">{assessment.testName}</h4>
                        <p className="text-sm text-base-content/70">
                          Type: {assessment.type.replace('_', ' ')}
                        </p>
                      </div>
                      <div className="text-right">
                        {assessment.score && assessment.maxScore ? (
                          <div className="text-lg font-bold">
                            {assessment.score}/{assessment.maxScore}
                          </div>
                        ) : (
                          <span className="badge badge-warning">Pending</span>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-sm text-base-content/70">
                  No assessments completed yet.
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="mt-6">
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h2 className="card-title">Quick Actions</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button className="btn btn-primary">
                View Schedule
              </button>
              <button className="btn btn-secondary">
                Take Assessment
              </button>
              <button className="btn btn-accent">
                View Progress
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
