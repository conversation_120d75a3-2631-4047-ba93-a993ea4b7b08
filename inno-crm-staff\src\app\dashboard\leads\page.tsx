import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { redirect } from 'next/navigation'
import { prisma } from '@/lib/database'
import { Phone, Plus, Search, Clock, CheckCircle } from 'lucide-react'

export default async function LeadsPage() {
  const session = await getServerSession(authOptions)

  if (!session) {
    redirect('/auth/signin')
  }

  // Check if user has permission to view leads
  if (!['ADMIN', 'MANAGER', 'RECEPTION'].includes(session.user.role)) {
    redirect('/dashboard')
  }

  // Fetch leads data
  const leads = await prisma.lead.findMany({
    take: 50,
    orderBy: { createdAt: 'desc' },
    include: {
      assignedTeacher: {
        include: {
          user: {
            select: {
              name: true,
            }
          }
        }
      },
      assignedGroup: {
        select: {
          name: true,
        }
      }
    }
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'NEW': return 'badge-info'
      case 'CALLING': return 'badge-warning'
      case 'CALL_COMPLETED': return 'badge-success'
      case 'GROUP_ASSIGNED': return 'badge-primary'
      case 'ARCHIVED': return 'badge-ghost'
      case 'NOT_INTERESTED': return 'badge-error'
      default: return 'badge-ghost'
    }
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-base-content">
            Lead Management
          </h1>
          <p className="text-base-content/70 mt-2">
            Track and manage potential students
          </p>
        </div>
        <button className="btn btn-primary">
          <Plus className="h-4 w-4" />
          Add Lead
        </button>
      </div>

      {/* Search and filters */}
      <div className="card bg-base-100 shadow-xl mb-6">
        <div className="card-body">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="form-control flex-1">
              <div className="input-group">
                <input
                  type="text"
                  placeholder="Search leads..."
                  className="input input-bordered flex-1"
                />
                <button className="btn btn-square">
                  <Search className="h-4 w-4" />
                </button>
              </div>
            </div>
            <select className="select select-bordered">
              <option value="">All Statuses</option>
              <option value="NEW">New</option>
              <option value="CALLING">Calling</option>
              <option value="CALL_COMPLETED">Call Completed</option>
              <option value="GROUP_ASSIGNED">Group Assigned</option>
              <option value="ARCHIVED">Archived</option>
              <option value="NOT_INTERESTED">Not Interested</option>
            </select>
            <select className="select select-bordered">
              <option value="">All Courses</option>
              <option value="English">English</option>
              <option value="IELTS">IELTS</option>
              <option value="SAT">SAT</option>
              <option value="Math">Math</option>
            </select>
          </div>
        </div>
      </div>

      {/* Leads table */}
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <div className="overflow-x-auto">
            <table className="table table-zebra w-full">
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Phone</th>
                  <th>Course Preference</th>
                  <th>Status</th>
                  <th>Assigned To</th>
                  <th>Follow Up</th>
                  <th>Created</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {leads.length > 0 ? (
                  leads.map((lead) => (
                    <tr key={lead.id}>
                      <td>
                        <div className="flex items-center space-x-3">
                          <div className="avatar placeholder">
                            <div className="bg-neutral text-neutral-content rounded-full w-8 h-8">
                              <span className="text-xs">
                                {lead.name.charAt(0)}
                              </span>
                            </div>
                          </div>
                          <div>
                            <div className="font-bold">{lead.name}</div>
                            {lead.source && (
                              <div className="text-xs text-base-content/70">
                                Source: {lead.source}
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td>
                        <a href={`tel:${lead.phone}`} className="link link-primary">
                          {lead.phone}
                        </a>
                      </td>
                      <td>
                        <span className="badge badge-outline">
                          {lead.coursePreference}
                        </span>
                      </td>
                      <td>
                        <span className={`badge ${getStatusColor(lead.status)}`}>
                          {lead.status.replace('_', ' ')}
                        </span>
                      </td>
                      <td>
                        {lead.assignedTeacher ? (
                          <span className="text-sm">
                            {lead.assignedTeacher.user.name}
                          </span>
                        ) : (
                          <span className="text-base-content/50">Unassigned</span>
                        )}
                      </td>
                      <td>
                        {lead.followUpDate ? (
                          <div className="flex items-center space-x-1">
                            <Clock className="h-4 w-4 text-warning" />
                            <span className="text-sm">
                              {new Date(lead.followUpDate).toLocaleDateString()}
                            </span>
                          </div>
                        ) : (
                          <span className="text-base-content/50">No follow-up</span>
                        )}
                      </td>
                      <td>
                        <span className="text-sm">
                          {new Date(lead.createdAt).toLocaleDateString()}
                        </span>
                      </td>
                      <td>
                        <div className="flex space-x-2">
                          <button className="btn btn-ghost btn-xs">
                            <Phone className="h-3 w-3" />
                          </button>
                          <button className="btn btn-ghost btn-xs">View</button>
                          <button className="btn btn-ghost btn-xs">Edit</button>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={8} className="text-center py-8">
                      <div className="flex flex-col items-center space-y-2">
                        <Phone className="h-12 w-12 text-base-content/30" />
                        <p className="text-base-content/70">No leads found</p>
                        <button className="btn btn-primary btn-sm">
                          <Plus className="h-4 w-4" />
                          Add First Lead
                        </button>
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}
