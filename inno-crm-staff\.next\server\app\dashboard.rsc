1:"$Sreact.fragment"
2:I[7555,[],""]
3:I[1295,[],""]
4:I[1112,["934","static/chunks/934-c3941a59724a394f.js","954","static/chunks/app/dashboard/layout-17e375423ceb49fe.js"],"Sidebar"]
5:I[9665,[],"OutletBoundary"]
8:I[4911,[],"AsyncMetadataOutlet"]
a:I[9665,[],"ViewportBoundary"]
c:I[9665,[],"MetadataBoundary"]
e:I[6614,[],""]
0:{"P":null,"b":"7VyuCnCtCoc8AiCo7jVOb","p":"","c":["","dashboard"],"i":false,"f":[[["",{"children":["dashboard",{"children":["__PAGE__",{}]},"$undefined","$undefined",true]}],["",["$","$1","c",{"children":[null,["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["dashboard",["$","$1","c",{"children":[null,["$","div",null,{"className":"drawer lg:drawer-open","children":[["$","input",null,{"id":"drawer-toggle","type":"checkbox","className":"drawer-toggle"}],["$","div",null,{"className":"drawer-content flex flex-col","children":[["$","div",null,{"className":"navbar bg-base-100 shadow-sm lg:hidden","children":[["$","div",null,{"className":"flex-none","children":["$","label",null,{"htmlFor":"drawer-toggle","className":"btn btn-square btn-ghost","children":["$","svg",null,{"className":"w-6 h-6","fill":"none","stroke":"currentColor","viewBox":"0 0 24 24","children":["$","path",null,{"strokeLinecap":"round","strokeLinejoin":"round","strokeWidth":2,"d":"M4 6h16M4 12h16M4 18h16"}]}]}]}],["$","div",null,{"className":"flex-1","children":["$","h1",null,{"className":"text-xl font-bold","children":"Staff Portal"}]}]]}],["$","main",null,{"className":"flex-1 bg-base-100","children":["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]}]]}],["$","$L4",null,{}]]}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"p-6","children":[["$","div",null,{"className":"mb-8","children":[["$","h1",null,{"className":"text-3xl font-bold text-base-content","children":"Staff Dashboard"}],["$","p",null,{"className":"text-base-content/70 mt-2","children":"Welcome to the Innovative Centre Staff Portal"}]]}],["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8","children":[["$","div",null,{"className":"card bg-base-100 shadow-xl","children":["$","div",null,{"className":"card-body","children":["$","div",null,{"className":"flex items-center justify-between","children":[["$","div",null,{"children":[["$","h2",null,{"className":"card-title text-sm font-medium text-base-content/70","children":"Total Students"}],["$","p",null,{"className":"text-2xl font-bold text-base-content","children":"0"}],["$","p",null,{"className":"text-sm text-base-content/60 mt-1","children":"Active students"}]]}],["$","div",null,{"className":"text-primary","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-graduation-cap h-8 w-8","aria-hidden":"true","children":[["$","path","j76jl0",{"d":"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z"}],["$","path","1lu8f3",{"d":"M22 10v6"}],["$","path","1r8lef",{"d":"M6 12.5V16a6 3 0 0 0 12 0v-3.5"}],"$undefined"]}]}]]}]}]}],["$","div",null,{"className":"card bg-base-100 shadow-xl","children":["$","div",null,{"className":"card-body","children":["$","div",null,{"className":"flex items-center justify-between","children":[["$","div",null,{"children":[["$","h2",null,{"className":"card-title text-sm font-medium text-base-content/70","children":"Teachers"}],["$","p",null,{"className":"text-2xl font-bold text-base-content","children":"0"}],["$","p",null,{"className":"text-sm text-base-content/60 mt-1","children":"Active teachers"}]]}],["$","div",null,{"className":"text-primary","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-users h-8 w-8","aria-hidden":"true","children":[["$","path","1yyitq",{"d":"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"}],["$","path","16gr8j",{"d":"M16 3.128a4 4 0 0 1 0 7.744"}],["$","path","kshegd",{"d":"M22 21v-2a4 4 0 0 0-3-3.87"}],["$","circle","nufk8",{"cx":"9","cy":"7","r":"4"}],"$undefined"]}]}]]}]}]}],["$","div",null,{"className":"card bg-base-100 shadow-xl","children":["$","div",null,{"className":"card-body","children":["$","div",null,{"className":"flex items-center justify-between","children":[["$","div",null,{"children":[["$","h2",null,{"className":"card-title text-sm font-medium text-base-content/70","children":"New Leads"}],["$","p",null,{"className":"text-2xl font-bold text-base-content","children":"0"}],["$","p",null,{"className":"text-sm text-base-content/60 mt-1","children":"This month"}]]}],["$","div",null,{"className":"text-primary","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-phone h-8 w-8","aria-hidden":"true","children":[["$","path","9njp5v",{"d":"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384"}],"$undefined"]}]}]]}]}]}],["$","div",null,{"className":"card bg-base-100 shadow-xl","children":["$","div",null,{"className":"card-body","children":["$","div",null,{"className":"flex items-center justify-between","children":[["$","div",null,{"children":[["$","h2",null,{"className":"card-title text-sm font-medium text-base-content/70","children":"Revenue"}],["$","p",null,{"className":"text-2xl font-bold text-base-content","children":"$$0"}],["$","p",null,{"className":"text-sm text-base-content/60 mt-1","children":"This month"}]]}],["$","div",null,{"className":"text-primary","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-dollar-sign h-8 w-8","aria-hidden":"true","children":[["$","line","7eqyqh",{"x1":"12","x2":"12","y1":"2","y2":"22"}],["$","path","1b0p4s",{"d":"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"}],"$undefined"]}]}]]}]}]}]]}],["$","div",null,{"className":"grid grid-cols-1 lg:grid-cols-2 gap-6","children":[["$","div",null,{"className":"card bg-base-100 shadow-xl","children":["$","div",null,{"className":"card-body","children":[["$","h2",null,{"className":"card-title","children":"Recent Activity"}],["$","div",null,{"className":"space-y-4","children":[["$","div",null,{"className":"alert alert-info","children":["$","span",null,{"children":"System is ready for use!"}]}],["$","div",null,{"className":"text-sm text-base-content/70","children":"No recent activity to display."}]]}]]}]}],["$","div",null,{"className":"card bg-base-100 shadow-xl","children":["$","div",null,{"className":"card-body","children":[["$","h2",null,{"className":"card-title","children":"Quick Actions"}],["$","div",null,{"className":"space-y-3","children":[["$","button",null,{"className":"btn btn-primary btn-block","children":"Add New Student"}],["$","button",null,{"className":"btn btn-secondary btn-block","children":"Create Group"}],["$","button",null,{"className":"btn btn-accent btn-block","children":"Add Lead"}]]}]]}]}]]}]]}],null,["$","$L5",null,{"children":["$L6","$L7",["$","$L8",null,{"promise":"$@9"}]]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","TUGv1Gi9DXXl_J6qKyX3rv",{"children":[["$","$La",null,{"children":"$Lb"}],null]}],["$","$Lc",null,{"children":"$Ld"}]]}],false]],"m":"$undefined","G":["$e","$undefined"],"s":false,"S":true}
f:"$Sreact.suspense"
10:I[4911,[],"AsyncMetadata"]
d:["$","div",null,{"hidden":true,"children":["$","$f",null,{"fallback":null,"children":["$","$L10",null,{"promise":"$@11"}]}]}]
7:null
b:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
6:null
9:{"metadata":[],"error":null,"digest":"$undefined"}
11:{"metadata":"$9:metadata","error":null,"digest":"$undefined"}
