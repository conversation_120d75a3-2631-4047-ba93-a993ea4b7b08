# Deployment Guide

This guide covers deploying both the Staff and Student portals to Vercel with custom domains.

## Prerequisites

1. Vercel account
2. Domain access for innovativecentre.net
3. Neon PostgreSQL databases (separate for staff and students)
4. Environment variables configured

## Environment Variables

### Staff Portal (.env.local)
```bash
# Database
DATABASE_URL="postgresql://username:password@host:port/crm-staff"

# NextAuth
NEXTAUTH_URL="https://staff.innovativecentre.net"
NEXTAUTH_SECRET="your-secret-key-here"

# Inter-server communication
STUDENTS_API_URL="https://students.innovativecentre.net"
STUDENTS_API_KEY="your-students-api-key"
```

### Student Portal (.env.local)
```bash
# Database
DATABASE_URL="postgresql://username:password@host:port/crm-students"

# NextAuth
NEXTAUTH_URL="https://students.innovativecentre.net"
NEXTAUTH_SECRET="your-secret-key-here"

# Inter-server communication
STAFF_API_URL="https://staff.innovativecentre.net"
STAFF_API_KEY="your-staff-api-key"
```

## Deployment Steps

### 1. Deploy Staff Portal

```bash
cd inno-crm-staff
vercel --prod
```

Configure domain: `staff.innovativecentre.net`

### 2. Deploy Student Portal

```bash
cd inno-crm-students
vercel --prod
```

Configure domain: `students.innovativecentre.net`

### 3. Database Setup

For each deployment:

```bash
# Push database schema
npm run db:push

# Seed initial data
npm run db:seed
```

### 4. Domain Configuration

In Vercel dashboard:
1. Go to project settings
2. Add custom domain
3. Configure DNS records:
   - `staff.innovativecentre.net` → Staff portal
   - `students.innovativecentre.net` → Student portal

### 5. Environment Variables in Vercel

Add all environment variables in Vercel dashboard under:
Project Settings → Environment Variables

## Security Features

### Staff Portal
- Restricted access (Admin, Manager, Teacher roles only)
- Enhanced security headers
- Frame protection (DENY)
- Content type protection

### Student Portal
- Student-only access
- Public-facing with authentication
- Frame protection (SAMEORIGIN)
- Content type protection

## Access Control

### Staff Portal Access
- **Admin**: Full system access
- **Manager**: Student and teacher management
- **Teacher**: Limited access to assigned groups
- **Reception**: Lead management
- **Cashier**: Payment management

### Student Portal Access
- **Students**: Personal dashboard, payments, progress tracking

## Testing Credentials

### Staff Portal
- Admin: `+998901234567` / `admin123`
- Manager: `+998901234568` / `manager123`
- Teacher: `+998901234569` / `teacher123`

### Student Portal
- Student 1: `+998901234570` / `student123`
- Student 2: `+998901234572` / `student456`

## Monitoring

- Check Vercel function logs for errors
- Monitor database connections
- Verify authentication flows
- Test inter-server communication

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Verify DATABASE_URL format
   - Check Neon database status
   - Ensure IP allowlist includes Vercel IPs

2. **Authentication Issues**
   - Verify NEXTAUTH_SECRET is set
   - Check NEXTAUTH_URL matches deployment URL
   - Ensure session configuration is correct

3. **Build Failures**
   - Check Prisma schema syntax
   - Verify all dependencies are installed
   - Review build logs in Vercel dashboard

## Maintenance

### Regular Tasks
- Monitor database performance
- Review access logs
- Update dependencies
- Backup database regularly
- Monitor Vercel usage limits

### Updates
1. Test changes locally
2. Deploy to preview environment
3. Run database migrations if needed
4. Deploy to production
5. Verify functionality

## Support

For deployment issues:
1. Check Vercel dashboard logs
2. Review database connection status
3. Verify environment variables
4. Test authentication flows
5. Contact system administrator if needed
