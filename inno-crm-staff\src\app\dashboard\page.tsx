import { DashboardCard } from "@/components/ui/dashboard-card"
import { Users, GraduationCap, DollarSign, Phone } from "lucide-react"
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { redirect } from 'next/navigation'
import { prisma } from '@/lib/database'

export default async function DashboardPage() {
  const session = await getServerSession(authOptions)

  if (!session) {
    redirect('/auth/signin')
  }

  // Fetch dashboard statistics
  const [
    totalStudents,
    totalTeachers,
    newLeads,
    totalRevenue,
    recentActivity
  ] = await Promise.all([
    prisma.studentReference.count({ where: { status: 'ACTIVE' } }),
    prisma.teacher.count(),
    prisma.lead.count({
      where: {
        status: 'NEW',
        createdAt: {
          gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
        }
      }
    }),
    prisma.paymentOverview.aggregate({
      _sum: { amount: true },
      where: {
        status: 'PAID',
        paidDate: {
          gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
        }
      }
    }),
    prisma.activityLog.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      include: { user: { select: { name: true } } }
    })
  ])
  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-base-content">
          Staff Dashboard
        </h1>
        <p className="text-base-content/70 mt-2">
          Welcome to the Innovative Centre Staff Portal
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <DashboardCard
          title="Total Students"
          value={totalStudents}
          description="Active students"
          icon={<GraduationCap className="h-8 w-8" />}
        />
        <DashboardCard
          title="Teachers"
          value={totalTeachers}
          description="Active teachers"
          icon={<Users className="h-8 w-8" />}
        />
        <DashboardCard
          title="New Leads"
          value={newLeads}
          description="This month"
          icon={<Phone className="h-8 w-8" />}
        />
        <DashboardCard
          title="Revenue"
          value={`$${totalRevenue._sum.amount?.toNumber() || 0}`}
          description="This month"
          icon={<DollarSign className="h-8 w-8" />}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h2 className="card-title">Recent Activity</h2>
            <div className="space-y-4">
              {recentActivity.length > 0 ? (
                recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-center space-x-3 p-3 bg-base-200 rounded-lg">
                    <div className="flex-1">
                      <p className="text-sm font-medium">
                        {activity.user.name} {activity.action.toLowerCase()} {activity.resource}
                      </p>
                      <p className="text-xs text-base-content/70">
                        {new Date(activity.createdAt).toLocaleString()}
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <div className="alert alert-info">
                  <span>No recent activity to display.</span>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h2 className="card-title">Quick Actions</h2>
            <div className="space-y-3">
              <button className="btn btn-primary btn-block">
                Add New Student
              </button>
              <button className="btn btn-secondary btn-block">
                Create Group
              </button>
              <button className="btn btn-accent btn-block">
                Add Lead
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
