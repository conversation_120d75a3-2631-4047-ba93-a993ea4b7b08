import { DashboardCard } from "@/components/ui/dashboard-card"
import { Users, GraduationCap, DollarSign, Phone } from "lucide-react"
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { redirect } from 'next/navigation'

export default async function DashboardPage() {
  const session = await getServerSession(authOptions)

  if (!session) {
    redirect('/auth/signin')
  }
  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-base-content">
          Staff Dashboard
        </h1>
        <p className="text-base-content/70 mt-2">
          Welcome to the Innovative Centre Staff Portal
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <DashboardCard
          title="Total Students"
          value="0"
          description="Active students"
          icon={<GraduationCap className="h-8 w-8" />}
        />
        <DashboardCard
          title="Teachers"
          value="0"
          description="Active teachers"
          icon={<Users className="h-8 w-8" />}
        />
        <DashboardCard
          title="New Leads"
          value="0"
          description="This month"
          icon={<Phone className="h-8 w-8" />}
        />
        <DashboardCard
          title="Revenue"
          value="$0"
          description="This month"
          icon={<DollarSign className="h-8 w-8" />}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h2 className="card-title">Recent Activity</h2>
            <div className="space-y-4">
              <div className="alert alert-info">
                <span>System is ready for use!</span>
              </div>
              <div className="text-sm text-base-content/70">
                No recent activity to display.
              </div>
            </div>
          </div>
        </div>

        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h2 className="card-title">Quick Actions</h2>
            <div className="space-y-3">
              <button className="btn btn-primary btn-block">
                Add New Student
              </button>
              <button className="btn btn-secondary btn-block">
                Create Group
              </button>
              <button className="btn btn-accent btn-block">
                Add Lead
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
