{"version": 4, "routes": {"/dashboard": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/dashboard", "dataRoute": "/dashboard.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "e68a0b89cec2f7ce7cfd1be9acf871d9", "previewModeSigningKey": "5d76688b6a6fcef67ffdd18d8f56db15e567089688fba76c0f99382ea65d71b7", "previewModeEncryptionKey": "432303998ead16f4492263a0c20df627a8126966e9b8c3ab2b2e77364b359edc"}}