import { cn } from "@/lib/utils"

interface DashboardCardProps {
  title: string
  value: string | number
  description?: string
  icon?: React.ReactNode
  className?: string
}

export function DashboardCard({
  title,
  value,
  description,
  icon,
  className
}: DashboardCardProps) {
  return (
    <div className={cn("card bg-base-100 shadow-xl", className)}>
      <div className="card-body">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="card-title text-sm font-medium text-base-content/70">
              {title}
            </h2>
            <p className="text-2xl font-bold text-base-content">
              {value}
            </p>
            {description && (
              <p className="text-sm text-base-content/60 mt-1">
                {description}
              </p>
            )}
          </div>
          {icon && (
            <div className="text-primary">
              {icon}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
