(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[954],{1112:(e,a,s)=>{"use strict";s.d(a,{Sidebar:()=>p});var r=s(5155),n=s(6874),t=s.n(n),d=s(5695),h=s(2596),l=s(9688),c=s(2713),i=s(7949),o=s(7580),m=s(9074),f=s(9420),b=s(5868),u=s(381);let x=[{name:"Dashboard",href:"/dashboard",icon:c.A},{name:"Students",href:"/dashboard/students",icon:i.A},{name:"Teachers",href:"/dashboard/teachers",icon:o.A},{name:"Groups",href:"/dashboard/groups",icon:m.A},{name:"Leads",href:"/dashboard/leads",icon:f.A},{name:"Payments",href:"/dashboard/payments",icon:b.A},{name:"Settings",href:"/dashboard/settings",icon:u.A}];function p(){let e=(0,d.usePathname)();return(0,r.jsxs)("div",{className:"drawer-side",children:[(0,r.jsx)("label",{htmlFor:"drawer-toggle",className:"drawer-overlay"}),(0,r.jsxs)("aside",{className:"min-h-full w-64 bg-base-200",children:[(0,r.jsx)("div",{className:"p-4",children:(0,r.jsx)("h1",{className:"text-xl font-bold text-primary",children:"Staff Portal"})}),(0,r.jsx)("ul",{className:"menu p-4 space-y-2",children:x.map(a=>{let s=a.icon,n=e===a.href;return(0,r.jsx)("li",{children:(0,r.jsxs)(t(),{href:a.href,className:function(){for(var e=arguments.length,a=Array(e),s=0;s<e;s++)a[s]=arguments[s];return(0,l.QP)((0,h.$)(a))}("flex items-center space-x-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",n?"bg-primary text-primary-content":"text-base-content hover:bg-base-300"),children:[(0,r.jsx)(s,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:a.name})]})},a.name)})})]})]})}},6714:(e,a,s)=>{Promise.resolve().then(s.bind(s,1112))}},e=>{var a=a=>e(e.s=a);e.O(0,[934,441,684,358],()=>a(6714)),_N_E=e.O()}]);