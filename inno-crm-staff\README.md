# Staff Portal - Innovative Centre CRM

A comprehensive staff management portal for Innovative Centre, built with Next.js 15, TypeScript, and Prisma.

## Features

- **Authentication**: Secure login with NextAuth.js
- **Role-based Access**: Admin, Manager, Teacher, Reception, Cashier roles
- **Student Management**: Track student records and enrollments
- **Teacher Management**: Manage teacher profiles and assignments
- **Lead Management**: Track and convert potential students
- **Payment Tracking**: Monitor financial transactions
- **Group Management**: Organize classes and schedules
- **Activity Logging**: Track all system activities

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js
- **Styling**: Tailwind CSS with Ripple UI
- **Deployment**: Vercel

## Getting Started

### Prerequisites

- Node.js 18+
- PostgreSQL database (Neon recommended)
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

4. Configure your database URL and other environment variables in `.env.local`

5. Set up the database:
```bash
npm run db:push
npm run db:seed
```

6. Start the development server:
```bash
npm run dev
```

The application will be available at [http://localhost:3001](http://localhost:3001)

## Environment Variables

```bash
DATABASE_URL="postgresql://username:password@host:port/database"
NEXTAUTH_URL="http://localhost:3001"
NEXTAUTH_SECRET="your-secret-key-here"
STUDENTS_API_URL="http://localhost:3002"
STUDENTS_API_KEY="your-students-api-key"
```

## Default Credentials

After running the seed script:

- **Admin**: `+998901234567` / `admin123`
- **Manager**: `+998901234568` / `manager123`
- **Teacher**: `+998901234569` / `teacher123`

## Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run db:push` - Push database schema
- `npm run db:studio` - Open Prisma Studio
- `npm run db:seed` - Seed database with test data

## Project Structure

```
src/
├── app/                 # Next.js App Router
│   ├── api/            # API routes
│   ├── auth/           # Authentication pages
│   └── dashboard/      # Main application pages
├── components/         # Reusable components
│   ├── layout/        # Layout components
│   └── ui/            # UI components
├── lib/               # Utility libraries
└── types/             # TypeScript type definitions
```

## Deployment

Deploy to Vercel with custom domain:

```bash
vercel --prod
```

Configure domain: `staff.innovativecentre.net`

See [DEPLOYMENT.md](../DEPLOYMENT.md) for detailed deployment instructions.

## License

Private - Innovative Centre
