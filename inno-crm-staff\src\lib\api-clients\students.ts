// API client for communicating with the students service

interface StudentData {
  id: string
  name: string
  phone: string
  email?: string
  level: string
  branch: string
  emergencyContact?: string
  status: string
}

interface CreateStudentRequest {
  name: string
  phone: string
  email?: string
  password: string
  level: string
  branch: string
  emergencyContact?: string
}

interface UpdateStudentRequest {
  name?: string
  phone?: string
  email?: string
  level?: string
  branch?: string
  emergencyContact?: string
  status?: string
}

class StudentsApiClient {
  private baseUrl: string
  private apiKey: string

  constructor() {
    this.baseUrl = process.env.STUDENTS_API_URL || 'http://localhost:3002'
    this.apiKey = process.env.STUDENTS_API_KEY || ''
  }

  private async request(endpoint: string, options: RequestInit = {}) {
    const url = `${this.baseUrl}/api${endpoint}`
    
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': this.apiKey,
        'X-Server-Source': 'staff',
        ...options.headers,
      },
    })

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: 'Unknown error' }))
      throw new Error(error.error || `HTTP ${response.status}`)
    }

    return response.json()
  }

  // Create a new student in the students database
  async createStudent(data: CreateStudentRequest): Promise<StudentData> {
    return this.request('/inter-server/students', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  // Update student information in the students database
  async updateStudent(id: string, data: UpdateStudentRequest): Promise<StudentData> {
    return this.request(`/inter-server/students/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  // Get student information from the students database
  async getStudent(id: string): Promise<StudentData> {
    return this.request(`/inter-server/students/${id}`)
  }

  // Sync student data between databases
  async syncStudent(id: string): Promise<StudentData> {
    return this.request(`/inter-server/students/${id}/sync`, {
      method: 'POST',
    })
  }

  // Get student payment information
  async getStudentPayments(id: string) {
    return this.request(`/inter-server/students/${id}/payments`)
  }

  // Get student attendance information
  async getStudentAttendance(id: string) {
    return this.request(`/inter-server/students/${id}/attendance`)
  }

  // Get student assessments
  async getStudentAssessments(id: string) {
    return this.request(`/inter-server/students/${id}/assessments`)
  }

  // Notify students service about group changes
  async notifyGroupChange(groupId: string, studentIds: string[]) {
    return this.request('/inter-server/groups/notify', {
      method: 'POST',
      body: JSON.stringify({
        groupId,
        studentIds,
        action: 'group_assignment'
      }),
    })
  }

  // Send message to students
  async sendMessage(data: {
    subject: string
    content: string
    recipientIds: string[]
    priority: string
    senderId: string
  }) {
    return this.request('/inter-server/messages', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }
}

export const studentsApiClient = new StudentsApiClient()
