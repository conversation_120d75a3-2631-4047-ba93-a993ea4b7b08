import "next-auth"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      name: string
      phone: string
      email?: string
      role: string
      studentProfile?: {
        id: string
        level: string
        branch: string
        emergencyContact?: string
        photoUrl?: string
        dateOfBirth?: string
        address?: string
        status: string
      }
    }
  }

  interface User {
    id: string
    name: string
    phone: string
    email?: string
    role: string
    studentProfile?: {
      id: string
      level: string
      branch: string
      emergencyContact?: string
      photoUrl?: string
      dateOfBirth?: string
      address?: string
      status: string
    }
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    role: string
    phone: string
    studentProfile?: {
      id: string
      level: string
      branch: string
      emergencyContact?: string
      photoUrl?: string
      dateOfBirth?: string
      address?: string
      status: string
    }
  }
}
