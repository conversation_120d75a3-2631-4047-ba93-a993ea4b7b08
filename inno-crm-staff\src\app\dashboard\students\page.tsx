'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { redirect } from 'next/navigation'
import { Users, Plus, Search, Edit, Trash2 } from 'lucide-react'
import { useStudents } from '@/lib/hooks/useStudents'
import { StudentModal } from '@/components/modals/StudentModal'

export default function StudentsPage() {
  const { data: session } = useSession()
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingStudent, setEditingStudent] = useState(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [branchFilter, setBranchFilter] = useState('')

  const {
    students,
    loading,
    error,
    pagination,
    fetchStudents,
    createStudent,
    updateStudent,
    deleteStudent,
  } = useStudents()

  if (!session) {
    redirect('/auth/signin')
  }

  // Check if user has permission to view students
  if (!['ADMIN', 'MANAGER', 'ACADEMIC_MANAGER'].includes(session.user.role)) {
    redirect('/dashboard')
  }

  const handleSearch = () => {
    fetchStudents({
      search: searchTerm,
      status: statusFilter,
      branch: branchFilter,
      page: 1,
    })
  }

  const handleAddStudent = () => {
    setEditingStudent(null)
    setIsModalOpen(true)
  }

  const handleEditStudent = (student: any) => {
    setEditingStudent(student)
    setIsModalOpen(true)
  }

  const handleDeleteStudent = async (id: string) => {
    if (confirm('Are you sure you want to delete this student?')) {
      try {
        await deleteStudent(id)
      } catch (error) {
        alert('Failed to delete student')
      }
    }
  }

  const handleModalSubmit = async (data: any) => {
    try {
      if (editingStudent) {
        await updateStudent(editingStudent.id, data)
      } else {
        await createStudent(data)
      }
      setIsModalOpen(false)
    } catch (error) {
      throw error // Let the modal handle the error
    }
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-base-content">
            Student Management
          </h1>
          <p className="text-base-content/70 mt-2">
            Manage student records and enrollments
          </p>
        </div>
        <button
          className="btn btn-primary"
          onClick={handleAddStudent}
        >
          <Plus className="h-4 w-4" />
          Add Student
        </button>
      </div>

      {/* Search and filters */}
      <div className="card bg-base-100 shadow-xl mb-6">
        <div className="card-body">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="form-control flex-1">
              <div className="input-group">
                <input
                  type="text"
                  placeholder="Search students..."
                  className="input input-bordered flex-1"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
                <button
                  className="btn btn-square"
                  onClick={handleSearch}
                >
                  <Search className="h-4 w-4" />
                </button>
              </div>
            </div>
            <select
              className="select select-bordered"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="">All Statuses</option>
              <option value="ACTIVE">Active</option>
              <option value="PAUSED">Paused</option>
              <option value="DROPPED">Dropped</option>
              <option value="COMPLETED">Completed</option>
            </select>
            <select
              className="select select-bordered"
              value={branchFilter}
              onChange={(e) => setBranchFilter(e.target.value)}
            >
              <option value="">All Branches</option>
              <option value="main">Main Branch</option>
            </select>
          </div>
        </div>
      </div>

      {/* Students table */}
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          {loading && (
            <div className="flex justify-center py-8">
              <span className="loading loading-spinner loading-lg"></span>
            </div>
          )}

          {error && (
            <div className="alert alert-error mb-4">
              <span>{error}</span>
            </div>
          )}

          <div className="overflow-x-auto">
            <table className="table table-zebra w-full">
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Phone</th>
                  <th>Level</th>
                  <th>Current Group</th>
                  <th>Status</th>
                  <th>Branch</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {students.length > 0 ? (
                  students.map((student) => (
                    <tr key={student.id}>
                      <td>
                        <div className="flex items-center space-x-3">
                          <div className="avatar placeholder">
                            <div className="bg-neutral text-neutral-content rounded-full w-8 h-8">
                              <span className="text-xs">
                                {student.name.charAt(0)}
                              </span>
                            </div>
                          </div>
                          <div>
                            <div className="font-bold">{student.name}</div>
                          </div>
                        </div>
                      </td>
                      <td>{student.phone}</td>
                      <td>
                        <span className="badge badge-outline">
                          {student.level || 'N/A'}
                        </span>
                      </td>
                      <td>{student.currentGroup?.name || 'Not assigned'}</td>
                      <td>
                        <span className={`badge ${
                          student.status === 'ACTIVE' ? 'badge-success' :
                          student.status === 'PAUSED' ? 'badge-warning' :
                          student.status === 'DROPPED' ? 'badge-error' :
                          'badge-info'
                        }`}>
                          {student.status}
                        </span>
                      </td>
                      <td>{student.branch}</td>
                      <td>
                        <div className="flex space-x-2">
                          <button
                            className="btn btn-ghost btn-xs"
                            onClick={() => handleEditStudent(student)}
                          >
                            <Edit className="h-3 w-3" />
                          </button>
                          {session?.user.role === 'ADMIN' && (
                            <button
                              className="btn btn-ghost btn-xs text-error"
                              onClick={() => handleDeleteStudent(student.id)}
                            >
                              <Trash2 className="h-3 w-3" />
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))
                ) : !loading && (
                  <tr>
                    <td colSpan={7} className="text-center py-8">
                      <div className="flex flex-col items-center space-y-2">
                        <Users className="h-12 w-12 text-base-content/30" />
                        <p className="text-base-content/70">No students found</p>
                        <button
                          className="btn btn-primary btn-sm"
                          onClick={handleAddStudent}
                        >
                          <Plus className="h-4 w-4" />
                          Add First Student
                        </button>
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {pagination.pages > 1 && (
            <div className="flex justify-center mt-6">
              <div className="btn-group">
                {Array.from({ length: pagination.pages }, (_, i) => i + 1).map((page) => (
                  <button
                    key={page}
                    className={`btn ${page === pagination.page ? 'btn-active' : ''}`}
                    onClick={() => fetchStudents({ page })}
                  >
                    {page}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Student Modal */}
      <StudentModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSubmit={handleModalSubmit}
        student={editingStudent}
      />
    </div>
  )
}
