import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('Seeding student users...')

  // Create test student users
  const student1Password = await bcrypt.hash('student123', 12)
  const student1 = await prisma.user.upsert({
    where: { phone: '+998901234570' },
    update: {},
    create: {
      phone: '+998901234570',
      email: '<EMAIL>',
      name: '<PERSON>',
      role: 'STUDENT',
      password: student1Password,
      studentProfile: {
        create: {
          level: 'A1',
          branch: 'main',
          emergencyContact: '+998901234571',
          status: 'ACTIVE',
        },
      },
    },
  })

  const student2Password = await bcrypt.hash('student456', 12)
  const student2 = await prisma.user.upsert({
    where: { phone: '+998901234572' },
    update: {},
    create: {
      phone: '+998901234572',
      email: '<EMAIL>',
      name: '<PERSON>',
      role: 'STUDENT',
      password: student2Password,
      studentProfile: {
        create: {
          level: 'B1',
          branch: 'main',
          emergencyContact: '+998901234573',
          status: 'ACTIVE',
        },
      },
    },
  })

  console.log('Student users created:')
  console.log('Student 1:', { phone: student1.phone, password: 'student123' })
  console.log('Student 2:', { phone: student2.phone, password: 'student456' })
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
